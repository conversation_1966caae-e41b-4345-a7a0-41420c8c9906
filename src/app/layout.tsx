// Root Layout - Required by Next.js
import "./globals.css";
import { ReactNode } from "react";
import QueryProvider from "@/providers/query-provider";
import { AuthSyncProvider } from '@/providers/auth-sync-provider';
import { Toaster } from "@/components/ui/toaster";
import { metadata } from './(user)/metadata';

export { metadata };

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <head>
        {/* Preload Prism.js CSS để tránh FOUC (Flash of Unstyled Content) */}
        <link
          rel="preload"
          href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css"
          as="style"
        />
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css"
          media="print"
          onLoad={(e) => {
            const target = e.target as HTMLLinkElement;
            target.media = 'all';
          }}
        />
        <noscript>
          <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css"
          />
        </noscript>
      </head>
      <body>
        <QueryProvider>
          <AuthSyncProvider>
            {children}
          </AuthSyncProvider>
        </QueryProvider>
        <Toaster />
      </body>
    </html>
  );
}
