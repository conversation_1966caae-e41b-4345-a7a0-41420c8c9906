@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    
    /* Custom colors */
    --navy-800: 30 41 99;
    --navy-900: 20 29 72;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    overflow-x: hidden;
  }
}

/* Prose Styles */
.prose {
  @apply max-w-none text-gray-700 dark:text-gray-300;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4 {
  @apply scroll-mt-24 font-bold text-gray-900 dark:text-gray-100;
}

.prose h2 {
  @apply text-2xl mt-8 mb-4;
}

.prose h3 {
  @apply text-xl mt-6 mb-3;
}

.prose h4 {
  @apply text-lg mt-4 mb-2;
}

.prose p {
  @apply mb-4 leading-relaxed;
}

.prose ul {
  @apply mb-4 space-y-2 list-none;
}

.prose ul li {
  @apply relative pl-6 text-gray-700 dark:text-gray-300;
}

.prose ul li::before {
  content: "—";
  @apply absolute left-0 text-gray-400;
}

.prose img {
  @apply rounded-lg shadow-lg my-8;
}

/* Smooth Scroll */
html {
  scroll-behavior: smooth;
}

/* Code Block Styles - Dracula Theme */
pre[class*="language-"],
code[class*="language-"] {
  color: #f8f8f2;
  background: none;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  tab-size: 4;
  hyphens: none;
}

pre[class*="language-"] {
  @apply p-4 my-6 overflow-auto rounded-lg;
  background: #282a36 !important;
  border: 1px solid #44475a;
}

/* Ensure code blocks inside pre have proper styling */
pre[class*="language-"] code[class*="language-"] {
  background: transparent !important;
  color: inherit;
  padding: 0;
  border-radius: 0;
  font-size: inherit;
}

/* Style for code blocks without specific language */
pre.language-none {
  background: #282a36 !important;
  color: #f8f8f2;
  border: 1px solid #44475a;
}

/* Dracula Theme Colors */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #6272a4;
}

.token.punctuation {
  color: #f8f8f2;
}

.token.namespace {
  opacity: 0.7;
}

.token.property,
.token.tag,
.token.constant,
.token.symbol,
.token.deleted {
  color: #ff79c6;
}

.token.boolean,
.token.number {
  color: #bd93f9;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #50fa7b;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string,
.token.variable {
  color: #f8f8f2;
}

.token.atrule,
.token.attr-value,
.token.function,
.token.class-name {
  color: #f1fa8c;
}

.token.keyword {
  color: #ff79c6;
}

.token.regex,
.token.important {
  color: #ffb86c;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

/* Prose code block overrides */
.prose pre {
  @apply bg-[#282a36] text-[#f8f8f2];
  margin: 0;
  padding: 1em;
  overflow-x: auto;
}

.prose code {
  @apply bg-[#282a36] text-[#f8f8f2] px-1.5 py-0.5 rounded;
  font-size: 0.875em;
}

.prose pre code {
  @apply p-0;
  background: none;
  border-radius: 0;
  font-size: inherit;
}

.mask-linear-gradient {
  mask-image: linear-gradient(to right, black 85%, transparent 100%);
  -webkit-mask-image: linear-gradient(to right, black 85%, transparent 100%);
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

@layer utilities {
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Custom bullet for prose - override default list style */
.prose ul {
  list-style: none !important;
  padding-left: 0 !important;
}

.prose ul > li {
  position: relative;
  padding-left: 1.5rem !important;
  margin-bottom: 0.5rem;
}

.prose ul > li::before {
  content: '•';
  position: absolute;
  left: 0;
  top: 0;
  color: #6b7280;
  font-size: 1.2em;
  font-weight: bold;
}

/* Dark mode support */
.dark .prose ul > li::before {
  color: #9ca3af;
}

/* Remove default marker */
.prose ul > li::marker {
  display: none;
}