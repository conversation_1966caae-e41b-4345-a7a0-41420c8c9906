const { PrismaClient } = require('@prisma/client');
const { ObjectId } = require('mongodb');

const prisma = new PrismaClient();

async function createTestPost() {
  try {
    // Tạo user admin n<PERSON>u ch<PERSON>a có
    let adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (!adminUser) {
      adminUser = await prisma.user.create({
        data: {
          name: 'Admin User',
          email: '<EMAIL>',
          password: '$2b$10$dummy.hash.for.testing', // dummy hash
          role: 'ADMIN'
        }
      });
      console.log('Created admin user:', adminUser.id);
    }

    // Tạo post test với code examples
    const testPost = await prisma.post.create({
      data: {
        title: 'Test Post - Syntax Highlighting',
        slug: 'test-post-syntax-highlighting',
        excerpt: 'This is a test post to check syntax highlighting functionality',
        content: `
          <h2>Testing Syntax Highlighting</h2>
          <p>This post contains various code examples to test syntax highlighting:</p>
          
          <h3>Java Example</h3>
          <pre class="language-java"><code>public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
        
        // Variables and data types
        int number = 42;
        String message = "Testing syntax highlighting";
        boolean isWorking = true;
        
        // Control structures
        if (isWorking) {
            for (int i = 0; i < 5; i++) {
                System.out.println("Iteration: " + i);
            }
        }
    }
}</code></pre>

          <h3>JavaScript Example</h3>
          <pre class="language-javascript"><code>// Modern JavaScript features
const greeting = (name) => {
    return \`Hello, \${name}!\`;
};

// Async/await example
async function fetchData() {
    try {
        const response = await fetch('/api/data');
        const data = await response.json();
        console.log('Data received:', data);
    } catch (error) {
        console.error('Error fetching data:', error);
    }
}

// Array methods
const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
const sum = numbers.reduce((acc, n) => acc + n, 0);</code></pre>

          <h3>Python Example</h3>
          <pre class="language-python"><code>import json
from typing import List, Dict

class DataProcessor:
    def __init__(self, data: List[Dict]):
        self.data = data
    
    def process_data(self) -> Dict:
        """Process the data and return statistics"""
        total = len(self.data)
        processed = []
        
        for item in self.data:
            if item.get('active', False):
                processed.append({
                    'id': item['id'],
                    'value': item['value'] * 2
                })
        
        return {
            'total': total,
            'processed': len(processed),
            'items': processed
        }

# Usage example
data = [
    {'id': 1, 'value': 10, 'active': True},
    {'id': 2, 'value': 20, 'active': False},
    {'id': 3, 'value': 30, 'active': True}
]

processor = DataProcessor(data)
result = processor.process_data()
print(json.dumps(result, indent=2))</code></pre>

          <h3>CSS Example</h3>
          <pre class="language-css"><code>/* Modern CSS with custom properties */
:root {
    --primary-color: #3b82f6;
    --secondary-color: #64748b;
    --border-radius: 0.5rem;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 1.5rem;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
}

.button:hover {
    background: color-mix(in srgb, var(--primary-color) 90%, black);
}</code></pre>

          <h3>SQL Example</h3>
          <pre class="language-sql"><code>-- Complex SQL query with joins and aggregations
SELECT 
    u.name,
    u.email,
    COUNT(p.id) as post_count,
    AVG(p.views) as avg_views,
    MAX(p.created_at) as last_post_date
FROM users u
LEFT JOIN posts p ON u.id = p.author_id
WHERE u.role = 'ADMIN'
    AND p.published = true
    AND p.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY u.id, u.name, u.email
HAVING post_count > 0
ORDER BY avg_views DESC, post_count DESC
LIMIT 10;</code></pre>

          <p>If syntax highlighting is working correctly, all code blocks above should have proper color coding!</p>
        `,
        published: true,
        isPinned: false,
        tags: ['test', 'syntax-highlighting', 'code'],
        authorId: adminUser.id
      }
    });

    console.log('✅ Test post created successfully!');
    console.log('Post ID:', testPost.id);
    console.log('Post slug:', testPost.slug);
    console.log('You can view it at: http://localhost:3001/posts/' + testPost.slug);

  } catch (error) {
    console.error('❌ Error creating test post:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestPost();
